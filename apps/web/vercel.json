{"regions": ["arn1"], "crons": [{"path": "/api/signicat/sync-agreements", "schedule": "*/10 6-23,0 * * *"}, {"path": "/api/etakst-check", "schedule": "*/5 0,6-23 * * *"}, {"path": "/api/etakst-check/reminder-backfill", "schedule": "0 */2 * * *"}, {"path": "/api/etakst-check/broker-tasks-completed", "schedule": "30 7 * * *"}, {"path": "/api/monitoring/health/update", "schedule": "*/5 * * * *"}, {"path": "/api/signicat/updated-agreements", "schedule": "15 6-23,0 * * *"}]}