import { getLatestValuation } from '@/actions/next/estate-documents'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import { sendLeads } from '@/app/api/signicat/util/send-leads'
import prisma from '@/db/prisma'

import { VitecRiskCheckCompletedEvent } from './event'

export async function processRiskCheckCompletedEvent(
  event: VitecRiskCheckCompletedEvent,
) {
  console.info(
    `Processing risk check completion for estate ${event.eventData.estateId}`,
  )

  if (event.isMockEvent) {
    console.info(
      `Skipping mock event processing for estate ${event.eventData.estateId}`,
    )
    return
  }

  try {
    const check = await prisma.etakst_check_queue.findFirst({
      where: { estate_id: event.eventData.estateId },
    })

    if (check?.is_complete) {
      console.info(
        `Risk check already marked as complete for estate ${event.eventData.estateId}`,
      )
      return
    }

    // Check if risk check is actually completed
    const isRiskCheckCompleted = await riskCompletionCheck(
      event.eventData.estateId,
    )

    if (!isRiskCheckCompleted) {
      console.warn(
        `Risk check marked as completed but check returned false for estate ${event.eventData.estateId}`,
      )
      return
    }

    // Reset last_checked_at so the etakst checker can re-process and sent the etakst (maybe)
    try {
      await prisma.etakst_check_queue.updateMany({
        where: { estate_id: event.eventData.estateId },
        data: { last_checked_at: null },
      })
      console.info(
        `Reset last_checked_at for estate ${event.eventData.estateId} after risk check completion event`,
      )
    } catch (e) {
      console.warn(
        `Failed to reset last_checked_at for estate ${event.eventData.estateId}:`,
        e,
      )
    }

    const listingAgreement = await prisma.listing_agreements.findUnique({
      where: {
        estate_id: event.eventData.estateId,
      },
      select: {
        id: true,
        signicat_document_id: true,
        receive_loan_offer: true,
        signing_finished_at: true,
        is_valuation: true,
      },
    })

    if (!listingAgreement) {
      console.info(
        `No listing agreement found for estate ${event.eventData.estateId}`,
      )
      return
    }

    if (!listingAgreement.is_valuation) {
      console.info(
        `Listing agreement is not a valuation for estate ${event.eventData.estateId}`,
      )
      return
    }

    if (
      !listingAgreement.signing_finished_at ||
      !listingAgreement.signicat_document_id
    ) {
      console.info(
        `Listing agreement not signed for estate ${event.eventData.estateId}`,
      )
      return
    }

    const valuation = await getLatestValuation(event.eventData.estateId)
    if (!valuation) {
      console.info(
        `No valuation document found for estate ${event.eventData.estateId}`,
      )
      return
    }

    // If there's a signed listing agreement that opted in for loan offers,
    // send the leads to partners
    if (listingAgreement.receive_loan_offer) {
      await sendLeads(
        {
          id: listingAgreement.signicat_document_id,
          externalId: event.eventData.estateId,
        },
        true,
      )
      console.info(
        `Leads sent for estate ${event.eventData.estateId} after risk check completion`,
      )
    } else {
      console.info(
        `Listing agreement opted out of loan offers for estate ${event.eventData.estateId}`,
      )
    }
  } catch (error) {
    console.error(
      `Error processing risk check completion for estate ${event.eventData.estateId}:`,
      error,
    )
  }
}
