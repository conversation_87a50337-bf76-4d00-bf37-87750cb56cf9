import { NextRequest, NextResponse } from 'next/server'

import { get, set } from '@/db/kv'
import { DAY, MINUTE } from '@/db/util'
import { CACHE_KEYS } from '@/lib/cache-keys'
import { providersForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/providers.ts'
import {
  ProviderDefinition,
  RawProviderStatus,
  withTimeout,
} from '@/server/model/AssignmentDocumentStatus/types'

export const dynamic = 'force-dynamic'
export const maxDuration = 30

export async function GET(
  req: NextRequest,
  props: { params: Promise<{ status: string }> },
) {
  const params = await props.params
  const status = params.status
  const estateIds = req.nextUrl.searchParams.getAll('estateId') ?? []
  const uniqueIds = [...new Set(estateIds)].filter(Boolean)

  if (uniqueIds.length === 0) {
    return new Response('Missing estateId', { status: 400 })
  }

  const selectedProviders = providersForStatus(status)

  let keepAlive: ReturnType<typeof setInterval> | undefined
  let aborted = false

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()

      const closeStream = () => {
        aborted = true
        if (keepAlive) clearInterval(keepAlive)
        try {
          controller?.close()
        } catch {
          // ignore
        }
      }

      const send = (obj: unknown) => {
        if (aborted) return
        try {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(obj)}\n\n`))
        } catch {
          closeStream()
        }
      }

      keepAlive = setInterval(() => {
        if (aborted) return
        try {
          controller.enqueue(encoder.encode(': keep-alive\n\n'))
        } catch {
          closeStream()
        }
      }, 15000)

      if (selectedProviders.length === 0) {
        closeStream()
        return
      }

      const tasks: Promise<void>[] = []

      const STALE_THRESHOLD_SECONDS = MINUTE / 6 // 10s

      async function refreshAndSend(estateId: string, p: ProviderDefinition) {
        if (req.signal.aborted) return
        const timeoutMs = p.timeoutMs ?? 5000
        const msg = await withTimeout(p.fetch(estateId), timeoutMs).catch(
          (e) => ({
            type: p.type,
            state: 'ERROR' as const,
            updatedAt: new Date().toISOString(),
            message: (e as Error)?.message ?? 'Ukjent feil',
          }),
        )
        const payload: RawProviderStatus & { cachedAt: string } = {
          ...msg,
          cachedAt: new Date().toISOString(),
        }

        // Update cache with long TTL (2 days)
        await set(
          CACHE_KEYS.ASSIGNMENT_DOCUMENT_STATUS.BY_ESTATE_AND_TYPE(
            estateId,
            p.type,
          ),
          payload,
          DAY * 2,
        ).catch((e) => {
          console.warn(`Cache set failed for ${estateId}, type ${p?.type}`, e)
        })

        send({ estateId, ...payload })
      }

      for (const estateId of uniqueIds) {
        for (const p of selectedProviders) {
          const task = (async () => {
            const key =
              CACHE_KEYS.ASSIGNMENT_DOCUMENT_STATUS.BY_ESTATE_AND_TYPE(
                estateId,
                p.type,
              )

            const cached = await get<RawProviderStatus & { cachedAt?: string }>(
              key,
            ).catch((e) => {
              console.warn(
                `Cache get failed for ${estateId}, type ${p?.type}`,
                e,
              )
            })

            // 1) Immediate response if cached
            if (cached) {
              send({ estateId, ...cached })
            }

            // 2) Background refresh if stale (>10s) or missing
            let shouldRefresh = !cached

            if (cached?.cachedAt) {
              const ts = Date.parse(cached.cachedAt)
              const ageSec = Number.isNaN(ts)
                ? Infinity
                : (Date.now() - ts) / 1000
              shouldRefresh = ageSec > STALE_THRESHOLD_SECONDS
            }

            if (shouldRefresh) {
              await refreshAndSend(estateId, p)
            }
          })().catch((e) => {
            console.error(
              `Unexpected error for estate ${estateId}, provider ${p?.type}`,
              e,
            )
          })

          tasks.push(task)
        }
      }

      // Close stream after all tasks settle (no throw)
      Promise.allSettled(tasks).finally(() => {
        closeStream()
      })

      req.signal.addEventListener('abort', () => {
        closeStream()
      })
    },
    cancel() {
      if (keepAlive) clearInterval(keepAlive)
      aborted = true
    },
  })

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
    },
  })
}
