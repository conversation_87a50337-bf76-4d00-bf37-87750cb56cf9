'use client'

import { AnimatePresence, motion } from 'framer-motion'
import {
  CheckIcon,
  FingerprintIcon,
  ShieldAlertIcon,
  ShieldCheckIcon,
} from 'lucide-react'
import { signOut } from 'next-auth/react'
import { signIn } from 'next-auth/webauthn'
import { EarlyAccessFeature } from 'posthog-js'
import { usePostHog } from 'posthog-js/react'
import React from 'react'

import { Alert, AlertDescription, AlertTitle } from '@nordvik/ui/alert'
import { Badge } from '@nordvik/ui/badge'
import { Button } from '@nordvik/ui/button'
import { Checkbox } from '@nordvik/ui/checkbox'
import { TextButton } from '@nordvik/ui/text-button'
import { useToast } from '@nordvik/ui/toaster'

import { useUserResetAllFlagsMutation } from '@/api/generated-client'
import { useUserContext } from '@/lib/UserContext'
import { useFeatureFlags } from '@/lib/analytics/feature-flag'
import { useTrackEvent } from '@/lib/analytics/track-event'

export function SettingsSections() {
  return (
    <div className="flex flex-col gap-6 mb-20">
      <ExperimentsSection />
      <PasskeySection />
      <FlagsSection />
    </div>
  )
}

function FlagsSection() {
  const trackEvent = useTrackEvent()
  const { mutateAsync, isPending, isSuccess } = useUserResetAllFlagsMutation()
  const { toast } = useToast()
  const successRef = React.useRef(isSuccess)
  successRef.current = successRef.current || isSuccess
  return (
    <Section
      title="Tilbakestill introduksjon"
      description="Har du avvist onboarding eller introduksjonsmeldinger? Tilbakestill for å vise alle bannere, hjelpetips og veiledninger igjen, og få en oppfriskning av viktige funksjoner."
      action={
        <Button
          size="sm"
          loading={isPending}
          variant="outline"
          iconStart={successRef.current ? <CheckIcon /> : undefined}
          onClick={async () => {
            await mutateAsync({})
            trackEvent('reset_user_flags')
            toast({
              title: 'Introduksjonen er tilbakestilt',
              variant: 'success',
            })
          }}
        >
          {successRef.current ? 'Tilbakestilt' : 'Tilbakestill introduksjon'}
        </Button>
      }
    />
  )
}
function ExperimentsSection() {
  const { user } = useUserContext()
  const featureFlags = useFeatureFlags()
  const [features, setFeatures] = React.useState<EarlyAccessFeature[]>()

  const posthog = usePostHog()

  React.useEffect(() => {
    posthog.getEarlyAccessFeatures(setFeatures, true, [
      'alpha',
      'beta',
      'concept',
    ])
  }, [posthog, user])

  return (
    <Section
      title="Eksperimentelle funksjoner"
      description="Vi utvikler stadig nye funksjoner for å forbedre opplevelsen i Nordvik Megler. Hvis du ønsker å prøve dem ut før lansering, kan du aktivere dem her. Skulle du oppleve problemer, kan funksjonene enkelt deaktiveres når som helst."
    >
      {features && features.length > 0 ? (
        <div className="flex flex-col gap-4">
          {features.map((feature) => {
            const key = feature.flagKey
            if (!key) return null
            const stageLabel = {
              alpha: 'Alfa',
              beta: 'Beta',
              concept: 'Konsept',
            }[feature.stage]
            const variant = (
              {
                concept: 'grey',
                alpha: 'bright-green',
                beta: 'dark-green',
              } as const
            )[feature.stage]
            return (
              <label key={feature.name} className="flex items-start gap-3">
                <Checkbox
                  checked={featureFlags[key] === true}
                  className="translate-y-[2px]"
                  onCheckedChange={(checked) => {
                    posthog.updateEarlyAccessFeatureEnrollment(
                      key,
                      Boolean(checked),
                    )
                    posthog.reloadFeatureFlags()
                  }}
                />
                <div>
                  <div className="flex items-center gap-2">
                    <h2 className="typo-body-md font-medium">{feature.name}</h2>
                    <Badge variant={variant}>{stageLabel}</Badge>
                  </div>
                  <p className="typo-body-md text-pretty">
                    {feature.description}{' '}
                    {feature.documentationUrl && (
                      <>
                        <TextButton
                          className="inline-block"
                          href={feature.documentationUrl}
                        >
                          Les mer
                        </TextButton>
                        .
                      </>
                    )}
                  </p>
                </div>
              </label>
            )
          })}
        </div>
      ) : (
        <div className="px-6 py-3 rounded-md bg-gray-muted">
          Vi har ingen funksjoner for øyeblikket som trenger å testes. Kom innom
          en annen gang!
        </div>
      )}
    </Section>
  )
}

function PasskeySection() {
  const trackEvent = useTrackEvent()
  const [state, setState] = React.useState<'idle' | 'success' | 'error'>('idle')
  return (
    <Section
      title="Legg til enklere og sikrere innlogging"
      description={
        <>
          En Passkey er en ny måte å logge inn på som er både enklere og sikrere
          enn passord. Du kan bruke fingeravtrykk, ansiktsgjenkjenning eller en
          PIN-kode for rask innlogging. Etter at du har lagt til Passkey, kan du
          velge det på innloggingssiden.{' '}
          <TextButton href="/hjelpesenter/passkey" className="inline-block">
            Les mer om Passkey
          </TextButton>
          .
        </>
      }
      action={
        <Button
          size="sm"
          variant="outline"
          iconStart={<FingerprintIcon />}
          onClick={async () => {
            try {
              const response = await signIn('passkey', {
                action: 'register',
                redirect: false,
              })
              if (response?.error) {
                throw new Error(response.error)
              }
              trackEvent('add_passkey')
              setState('success')
            } catch (error) {
              trackEvent('add_passkey_error', {
                error: error?.message || error,
              })
              setState('error')
              console.error('Failed to register passkey', error)
            }
          }}
        >
          Legg til Passkey
        </Button>
      }
    >
      <AnimatePresence>
        {state !== 'idle' && (
          <motion.div
            style={{ overflow: 'hidden' }}
            initial={{ height: 0, scale: 0.9 }}
            animate={{ height: 'auto', scale: 1 }}
            transition={{ type: 'spring', stiffness: 300, damping: 20 }}
          >
            {state === 'success' ? (
              <PasskeySuccessAlert />
            ) : state === 'error' ? (
              <PasskeyErrorAlert />
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </Section>
  )
}

function PasskeySuccessAlert() {
  return (
    <Alert variant="success" Icon={ShieldCheckIcon}>
      <AlertTitle>Logg inn med din nye Passkey</AlertTitle>
      <AlertDescription>
        Vi anbefaler at du{' '}
        <TextButton
          onClick={() => {
            signOut()
          }}
          className="inline-block"
        >
          logger inn
        </TextButton>{' '}
        på nytt for å teste din nye Passkey. Dette sikrer en sømløs og trygg
        innlogging, samtidig som du blir kjent med den nye innloggingsmetoden.
      </AlertDescription>
    </Alert>
  )
}

function PasskeyErrorAlert() {
  return (
    <Alert variant="warning" Icon={ShieldAlertIcon}>
      <AlertTitle>Passkey kunne ikke legges til</AlertTitle>
      <AlertDescription>
        Det ser ut til at noe gikk galt under oppsettet av din Passkey. Det kan
        være på grunn av at du allerede har lagt til Passkey, eller så kan du
        prøve igjen ved å følge{' '}
        <TextButton href="/hjelpesenter/passkey" className="inline-block">
          artikkelen vår
        </TextButton>{' '}
        om hvordan du legger til Passkey. Du kan også ta kontakt med oss via
        chat for hjelp.
      </AlertDescription>
    </Alert>
  )
}

function Section({
  title,
  description,
  children,
  action,
}: {
  title: React.ReactNode
  description: React.ReactNode
  children?: React.ReactNode
  action?: React.ReactNode
}) {
  return (
    <div className="flex flex-col gap-4 border border-muted p-6 rounded-md">
      <div className="flex flex-col gap-1">
        <h3 className="typo-title-sm">{title}</h3>
        <p className="typo-body-md max-w-prose">{description}</p>
      </div>
      {children}
      <div className="flex justify-end empty:hidden mt-3 -mx-6 py-3 px-6 -mb-6 border-t border-muted">
        {action}
      </div>
    </div>
  )
}
