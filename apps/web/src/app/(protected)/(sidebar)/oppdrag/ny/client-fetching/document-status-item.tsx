'use client'

import { Check, Circle, X } from 'lucide-react'
import React from 'react'

import { cn } from '@nordvik/theme/cn'

import { Icons } from '@/components/icons'

import type { StatusItem } from './use-document-statuses-batch-sse'

function pillColor(state: string) {
  switch (state) {
    case 'COMPLETE':
      return 'bg-success-muted'
    case 'ERROR':
      return 'bg-danger-subtle'
    case 'STARTED':
      return 'bg-[#FFEDB2]'
    default:
      return 'bg-background-root'
  }
}

function icons(state: string) {
  switch (state) {
    case 'COMPLETE':
      return <Check className="size-3 ink-success" aria-hidden />
    case 'ERROR':
      return <X className="size-3 ink-danger" aria-label="Feil" />
    case 'STARTED':
      return (
        <Icons.standby className="size-3" color="#FFEDB2" aria-label="Pågår" />
      )
    case 'NONE':
      return <Circle className="size-3 ink-muted" aria-label="Ikke startet" />
    default:
      return <Circle className="size-3 ink-muted" aria-label="Ikke startet" />
  }
}

export function DocumentStatusItemFallback({ name }: { name: string }) {
  return (
    <span className="inline-flex items-center gap-1 rounded-md  px-2 py-0.5 text-xs capitalize leading-5 bg-background-root ">
      {name}
      <span
        className="inline-block h-3 w-3 animate-spin rounded-full border-[2px] border-t-transparent"
        aria-label="Laster"
      />
    </span>
  )
}

export function DocumentStatusItem({
  name,
  item,
}: {
  estateId: string
  name: string
  item?: StatusItem
}) {
  if (!item) {
    return <DocumentStatusItemFallback name={name} />
  }
  return (
    <div
      title={item.message ?? undefined}
      className={cn(
        'flex rounded-md  px-2 py-0.5 text-xs leading-5 items-center gap-1',
        pillColor(item.state ?? 'PENDING'),
      )}
    >
      {icons(item.state)}
      {name}
    </div>
  )
}
