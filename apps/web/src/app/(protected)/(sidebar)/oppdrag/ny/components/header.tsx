'use client'

import { parseAsInteger, useQueryState } from 'nuqs'

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@nordvik/ui/tabs'

import { useEstatesTab, useEstatesView } from './assignments-table-parts/hooks'
import { TabSlug, ViewType } from './assignments-table-parts/util'

export function Header({ children }: { children: React.ReactNode }) {
  const [tab, setTab] = useEstatesTab()
  const [view, setView] = useEstatesView()
  const [, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  return (
    <div className="space-y-4">
      <Tabs
        value={view ?? 'ansatt'}
        onValueChange={(v) => setView(v as ViewType, { shallow: false })}
      >
        <TabsList>
          <TabsTrigger value="ansatt">Mine oppdrag</TabsTrigger>
          <TabsTrigger value="kontor">Kontoret</TabsTrigger>
        </TabsList>
      </Tabs>

      <Tabs
        value={tab ?? 'klargjoring'}
        onValueChange={(v) => {
          setTab(v as TabSlug, { shallow: false })
          setPage(1)
        }}
      >
        <TabsList>
          <TabsTrigger value="verdivurdering">Verdivurdering</TabsTrigger>
          <TabsTrigger value="innslag">Innslag</TabsTrigger>
          <TabsTrigger value="klargjoring">Klargjøring</TabsTrigger>
          <TabsTrigger value="til-salg">Til salg</TabsTrigger>
          <TabsTrigger value="solgt">Solgt</TabsTrigger>
          <TabsTrigger value="arkiv">Arkiv</TabsTrigger>
        </TabsList>
        <TabsContent value={tab ?? 'klargjoring'} className="mt-4">
          {children}
        </TabsContent>
      </Tabs>
    </div>
  )
}
