import { getISOWeek } from 'date-fns'
import React from 'react'

import { TableRow } from '@nordvik/ui/table'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import { getColumnsForTab } from './columns'
import { MemoRow } from './row'

type Group = {
  key: number
  weekStart: Date
  estates: GQLEstatesOverviewItemFragment[]
}

export function GroupedBody({
  groups,
  withoutPublishDate,
  currentTab,
}: {
  groups: Group[]
  withoutPublishDate: GQLEstatesOverviewItemFragment[]
  currentTab?: GQLEstateTabFilter
}) {
  const colCount = getColumnsForTab(currentTab).length
  return (
    <>
      {groups.map((g) => (
        <React.Fragment key={g.key}>
          <TableRow>
            <td
              colSpan={colCount}
              className="py-2 pl-4 font-medium bg-root-muted"
            >
              {`Uke ${getISOWeek(g.weekStart)}`}
            </td>
          </TableRow>
          {g.estates.map((estate) => (
            <MemoRow
              key={estate.estateId}
              estate={estate}
              currentTab={currentTab}
            />
          ))}
        </React.Fragment>
      ))}

      {withoutPublishDate.length > 0 && (
        <>
          <TableRow>
            <td
              colSpan={colCount}
              className="py-2 pl-4 font-medium bg-root-muted"
            >
              Uten publiseringsdato
            </td>
          </TableRow>
          {withoutPublishDate.map((estate) => (
            <MemoRow
              key={estate.estateId}
              estate={estate}
              currentTab={currentTab}
            />
          ))}
        </>
      )}
    </>
  )
}
