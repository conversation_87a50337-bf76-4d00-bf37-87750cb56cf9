import { startOfWeek } from 'date-fns'

import {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

// These types should probably be defined in graphql schema instead
export type ViewType = 'ansatt' | 'kontor'

// These types should probably be defined in graphql schema instead
export type TabSlug =
  | 'verdivurdering'
  | 'innslag'
  | 'klargjoring'
  | 'til-salg'
  | 'solgt'
  | 'arkiv'

export const TAB_SLUG_TO_ENUM: Record<TabSlug, GQLEstateTabFilter> = {
  verdivurdering: GQLEstateTabFilter.Valuation,
  innslag: GQLEstateTabFilter.Requested,
  klargjoring: GQLEstateTabFilter.InPreparation,
  'til-salg': GQLEstateTabFilter.ForSale,
  solgt: GQLEstateTabFilter.Sold,
  arkiv: GQLEstateTabFilter.Archived,
}

export function groupByWeek(estates: GQLEstatesOverviewItemFragment[]) {
  const map = new Map<
    number,
    { weekStart: Date; estates: GQLEstatesOverviewItemFragment[] }
  >()

  // Only consider estates with a publish date, group by ISO week starting Monday
  const withDate = estates
    .filter((e) => !!e.marketingStart)
    .sort((a, b) => {
      const da = a.marketingStart ? new Date(a.marketingStart).getTime() : 0
      const db = b.marketingStart ? new Date(b.marketingStart).getTime() : 0
      return da - db
    })

  for (const e of withDate) {
    const d = e.marketingStart ? new Date(e.marketingStart) : undefined
    if (!d) continue
    const ws = startOfWeek(d, { weekStartsOn: 1 })
    const key = ws.getTime()
    if (!map.has(key)) map.set(key, { weekStart: ws, estates: [] })
    map.get(key)!.estates.push(e)
  }

  const pastFutureAndCurrent = Array.from(map.values()).sort(
    (a, b) => a.weekStart.getTime() - b.weekStart.getTime(),
  )

  return pastFutureAndCurrent.map((g) => ({
    key: g.weekStart.getTime(),
    ...g,
  }))
}
