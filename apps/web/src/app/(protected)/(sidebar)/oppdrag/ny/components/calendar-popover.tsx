'use client'

import { CalendarDays } from 'lucide-react'
import React from 'react'

import { Calendar } from '@nordvik/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@nordvik/ui/popover'

export function CalendarPopover({ publishDate }: { publishDate?: string }) {
  const [date, setDate] = React.useState<Date | undefined>(
    publishDate ? new Date(publishDate) : undefined,
  )
  return (
    <Popover>
      <PopoverTrigger className="w-[160px]">
        <div className="flex items-center justify-between rounded-md border border-subtle px-2 py-1.5 text-left text-sm">
          <span>{date ? date.toLocaleDateString('no-NO') : 'Sett dato'}</span>
          <CalendarDays className="size-4" />
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-2">
        <Calendar mode="single" selected={date} onSelect={setDate} />
      </PopoverContent>
    </Popover>
  )
}
