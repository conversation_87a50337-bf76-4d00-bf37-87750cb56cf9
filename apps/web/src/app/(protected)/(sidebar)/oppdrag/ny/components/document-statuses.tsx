import React from 'react'

import ErrorBoundary from '@/components/error-boundary'
import { providers } from '@/server/model/AssignmentDocumentStatus/helpers/providers.ts'

import {
  DocumentStatusItemBoundary,
  DocumentStatusItemFallback,
} from './document-status-item'

interface Props {
  estateId: string
}

// Server wrapper: fetch once (may serve cached) and hand off to client for polling.
// Previous aggregated preloading removed to allow progressive, per-item streaming.
// Keeping a lightweight async component in case future preloading is reintroduced.
export async function DocumentStatuses({ estateId }: Props) {
  return (
    <div className="flex flex-wrap gap-2">
      {providers.map((p) => (
        <DocumentStatusItemBoundary
          key={p.type}
          estateId={estateId}
          type={p.type}
        />
      ))}
    </div>
  )
}

export function DocumentStatusesFallback() {
  return (
    <div className="flex gap-2 flex-wrap">
      {providers.map((p) => (
        <DocumentStatusItemFallback key={p.type} type={p.type} />
      ))}
    </div>
  )
}

export function DocumentStatusesBoundary({ estateId }: Props) {
  // Keep outer boundary for catastrophic errors; inner per-item boundaries handle individual loaders.
  return (
    <ErrorBoundary>
      <React.Suspense fallback={<DocumentStatusesFallback />}>
        <DocumentStatuses estateId={estateId} />
      </React.Suspense>
    </ErrorBoundary>
  )
}
