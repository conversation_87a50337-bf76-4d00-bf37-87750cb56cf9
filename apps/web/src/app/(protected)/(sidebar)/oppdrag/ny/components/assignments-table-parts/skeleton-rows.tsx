import React from 'react'

import { TableCell, TableRow } from '@nordvik/ui/table'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { getColumnsForTab } from './columns'

type Props = {
  colSpan?: number
  currentTab?: GQLEstateTabFilter
}

export function SkeletonRows({ colSpan, currentTab }: Props) {
  const count = colSpan ?? getColumnsForTab(currentTab).length
  return (
    <>
      <TableRow>
        <TableCell colSpan={count}>
          <div className="flex animate-pulse flex-col gap-2 py-3">
            <div className="h-4 w-full rounded bg-root-muted" />
            <div className="flex w-full gap-2">
              <div className="h-4 w-1/4 rounded bg-root-muted" />
              <div className="h-4 w-1/4 rounded bg-root-muted" />
              <div className="h-4 w-1/4 rounded bg-root-muted" />
              <div className="h-4 w-1/4 rounded bg-root-muted" />
            </div>
          </div>
        </TableCell>
      </TableRow>
      {Array.from({ length: 3 }).map((_, i) => (
        <TableRow key={`skeleton-${i}`}>
          {Array.from({ length: count }).map((__, j) => (
            <td
              key={`sk-${i}-${j}`}
              className={`py-3 ${j === 0 ? 'pl-4' : ''} ${j === count - 1 ? 'pr-3' : ''}`}
            >
              <div className="h-4 w-24 animate-pulse rounded bg-root-muted" />
            </td>
          ))}
        </TableRow>
      ))}
    </>
  )
}
