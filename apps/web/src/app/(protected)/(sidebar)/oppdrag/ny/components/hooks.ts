import { parseAsStringLiteral, useQueryState } from 'nuqs'

export type ViewType = 'ansatt' | 'kontor'

export type TabSlug =
  | 'verdivurdering'
  | 'innslag'
  | 'klargjoring'
  | 'til-salg'
  | 'solgt'
  | 'arkiv'

export function useEstatesTab() {
  return useQueryState<TabSlug>(
    'tab',
    parseAsStringLiteral([
      'verdivurdering',
      'innslag',
      'klargjoring',
      'til-salg',
      'solgt',
      'arkiv',
    ]).withDefault('klargjoring'),
  )
}

export function useEstatesView() {
  return useQueryState<ViewType>(
    'view',
    parseAsStringLiteral(['kontor', 'ansatt']).withDefault('ansatt'),
  )
}
