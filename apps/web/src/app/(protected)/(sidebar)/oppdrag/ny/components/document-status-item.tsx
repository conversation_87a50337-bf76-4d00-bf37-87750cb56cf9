import React from 'react'

import { cn } from '@nordvik/theme/cn'

import ErrorBoundary from '@/components/error-boundary'
import { cachedAssignmentDocumentStatus } from '@/server/model/AssignmentDocumentStatus/factory'

function pillColor(state: string) {
  switch (state) {
    case 'COMPLETE':
      return 'bg-success-muted'
    case 'ERROR':
      return 'bg-danger-subtle'
    default:
      return 'bg-background-root'
  }
}

interface FallbackProps {
  type: string
}

export function DocumentStatusItemFallback({ type }: FallbackProps) {
  const label = type.toLowerCase().replace(/_/g, ' ')
  return (
    <span className="inline-flex items-center gap-1 rounded-md border px-2 py-0.5 text-xs capitalize leading-5 bg-background-root">
      {label}
      <span
        className="inline-block h-3 w-3 animate-spin rounded-full border-[2px] border-t-transparent"
        aria-label="Laster"
      />
    </span>
  )
}

interface ItemProps {
  estateId: string
  type: string
}

export async function DocumentStatusItem({ estateId, type }: ItemProps) {
  const item = await cachedAssignmentDocumentStatus(estateId, type)

  return (
    <span
      title={item.message ?? undefined}
      className={cn(
        'rounded-md border px-2 py-0.5 text-xs capitalize leading-5',
        pillColor(item.state),
      )}
    >
      {item.type.toLowerCase().replace(/_/g, ' ')}
    </span>
  )
}

export function DocumentStatusItemBoundary(props: ItemProps) {
  return (
    <ErrorBoundary>
      <React.Suspense
        fallback={<DocumentStatusItemFallback type={props.type} />}
      >
        <DocumentStatusItem {...props} />
      </React.Suspense>
    </ErrorBoundary>
  )
}
