'use client'

import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'

import { AssignmentsTable } from './assignments-table'
import { TabSlug } from './hooks'

export const TAB_SLUG_TO_ENUM: Record<TabSlug, GQLEstateTabFilter> = {
  verdivurdering: GQLEstateTabFilter.Valuation,
  innslag: GQLEstateTabFilter.Requested,
  klargjoring: GQLEstateTabFilter.InPreparation,
  'til-salg': GQLEstateTabFilter.ForSale,
  solgt: GQLEstateTabFilter.Sold,
  arkiv: GQLEstateTabFilter.Archived,
}

export default function NewAssignmentsPage() {
  return <AssignmentsTable />
}
