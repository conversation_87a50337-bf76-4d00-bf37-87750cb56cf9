import type { Metada<PERSON> } from 'next'
import { notFound } from 'next/navigation'

import { Main } from '@/components/main'
import { getFeatureFlag } from '@/lib/analytics/feature-flag.server'

import { AssignmentsTable } from './components/assignments-table'
import { Header } from './components/header'

export const metadata: Metadata = {
  title: 'Mine oppdrag',
  description: 'Oppdragsoversikt med tydelige statuser og filtrering',
}

const FLAG_NAME = 'assignments-overview-v2'

export default async function Page() {
  // Feature flag gate (local enable via NEXT_PUBLIC_FEATURE_FLAGS, or PostHog remote flag)
  const enabled = await getFeatureFlag(FLAG_NAME)
  if (!enabled) {
    // Hide the route entirely when flag disabled
    notFound()
  }

  // Server component orchestrates only the critical estate list fetch.
  // Document statuses are fetched in parallel per estate inside Suspense
  // boundaries defined in child components.
  return (
    <Main>
      <Header>
        <AssignmentsTable />
      </Header>
    </Main>
  )
}
