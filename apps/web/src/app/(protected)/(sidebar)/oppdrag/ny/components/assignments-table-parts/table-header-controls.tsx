import React from 'react'

import { TableHeader, TableRow } from '@nordvik/ui/table'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { getColumnsForTab, headerClassFor, headerLabelFor } from './columns'

export function TableHeaderControls({
  ariaSortFor,
  onSortAddress,
  onSortPublish,
  currentTab,
}: {
  ariaSortFor: (
    key: 'address' | 'publish',
  ) => 'ascending' | 'descending' | 'none'
  onSortAddress: () => void
  onSortPublish: () => void
  currentTab?: GQLEstateTabFilter
}) {
  const cols = getColumnsForTab(currentTab)
  return (
    <TableHeader>
      <TableRow>
        {cols.map((c) => {
          const label = headerLabelFor(c, currentTab)
          const cls = headerClassFor(c)
          const isSortable = c === 'address' || c === 'publish'
          const sortAria =
            c === 'address'
              ? ariaSortFor('address')
              : c === 'publish'
                ? ariaSortFor('publish')
                : undefined
          const onClick =
            c === 'address'
              ? onSortAddress
              : c === 'publish'
                ? onSortPublish
                : undefined
          return (
            <th
              key={c}
              className={`${cls} ${isSortable ? 'cursor-pointer select-none' : ''}`}
              {...(isSortable
                ? { onClick, 'aria-sort': sortAria, role: 'columnheader' }
                : {})}
            >
              {label}
            </th>
          )
        })}
      </TableRow>
    </TableHeader>
  )
}
