import React from 'react'

import { <PERSON>Header, TableRow } from '@nordvik/ui/table'

import type { GQLEstateTabFilter } from '@/api/generated-client'

import { SortKey } from '../assignments-table'

import { getColumnsForTab, headerClassFor, headerLabelFor } from './columns'

export function TableHeaderControls({
  ariaSortFor,
  onSortAddress,
  onSortPublish,
  currentTab,
}: {
  ariaSortFor: (key: SortKey) => 'ascending' | 'descending' | 'none'
  onSortAddress: () => void
  onSortPublish: () => void
  currentTab?: GQLEstateTabFilter
}) {
  const cols = getColumnsForTab(currentTab)

  const isSortableCol = (col: string): col is SortKey =>
    col === 'address' || col === 'publish'

  const getSortProps = (
    col: string,
  ): React.ThHTMLAttributes<HTMLTableCellElement> => {
    if (!isSortableCol(col)) return {}
    const onClick = col === 'address' ? onSortAddress : onSortPublish
    return {
      onClick,
      'aria-sort': ariaSortFor(col),
      role: 'columnheader',
    }
  }

  return (
    <TableHeader>
      <TableRow>
        {cols.map((c) => {
          const label = headerLabelFor(c, currentTab)
          const baseClass = headerClassFor(c)
          const sortable = isSortableCol(c)
          const className = `${baseClass}${sortable ? ' cursor-pointer select-none' : ''}`
          return (
            <th key={c} className={className} {...getSortProps(c)}>
              {label}
            </th>
          )
        })}
      </TableRow>
    </TableHeader>
  )
}
