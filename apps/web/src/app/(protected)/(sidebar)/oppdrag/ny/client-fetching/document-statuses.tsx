'use client'

import React from 'react'

import { providerTypesForStatus } from '@/server/model/AssignmentDocumentStatus/helpers/providers'
import { EstateStatusKey } from '@/server/model/AssignmentDocumentStatus/types'

import { useEstatesTab } from '../components/hooks'
import { TAB_SLUG_TO_ENUM } from '../components/new-assignments-page'

import { DocumentStatusItem } from './document-status-item'
import { useEstateDocumentStatuses } from './use-document-statuses-batch-sse'

export const DocumentStatuses = React.memo(function DocumentStatuses({
  estateId,
}: {
  estateId: string
}) {
  const itemsMap = useEstateDocumentStatuses(estateId)
  const [tab] = useEstatesTab()
  const types = providerTypesForStatus(
    TAB_SLUG_TO_ENUM[tab] as unknown as EstateStatusKey,
  )

  return (
    <div className="flex flex-wrap gap-2">
      {types.map(({ type, name }) => (
        <DocumentStatusItem
          key={type}
          estateId={estateId}
          name={name}
          item={itemsMap[type]}
        />
      ))}
    </div>
  )
})
