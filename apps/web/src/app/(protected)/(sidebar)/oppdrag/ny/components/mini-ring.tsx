// Small circular progress + fraction like "6/8"
export function MiniRing({ progress }: { progress: number }) {
  const radius = 9
  const stroke = 3
  const circumference = 2 * Math.PI * radius
  const offset = circumference * (1 - progress)
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" className="shrink-0">
      <circle
        cx="12"
        cy="12"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-subtle"
      />
      <circle
        cx="12"
        cy="12"
        r={radius}
        strokeWidth={stroke}
        fill="none"
        className="stroke-success-emphasis"
        strokeDasharray={circumference}
        strokeDashoffset={offset}
        transform="rotate(-90 12 12)"
        strokeLinecap="round"
      />
    </svg>
  )
}
