'use client'

import React from 'react'

import { GQLEstateTabFilter } from '@/api/generated-client'

export type StatusItem = {
  type: string
  state: string
  updatedAt: string
  message?: string
}

type EstateStatusMap = Record<string, Record<string, StatusItem>>

function dedupeAndSort(ids: string[]) {
  return [...new Set(ids.filter(Boolean))].sort()
}

function useBatchSSE(estateIds: string[], status: GQLEstateTabFilter) {
  const [map, setMap] = React.useState<EstateStatusMap>({})

  const key = React.useMemo(
    () =>
      JSON.stringify({
        ids: dedupeAndSort(estateIds),
        status,
      }),
    [estateIds, status],
  )

  React.useEffect(() => {
    const ids = dedupeAndSort(estateIds)
    if (ids.length === 0) return

    const url = new URL(
      `/api/assignment-document-statuses/${encodeURIComponent(String(status))}`,
      window.location.origin,
    )

    for (const id of ids) {
      url.searchParams.append('estateId', id)
    }

    const es = new EventSource(url)

    es.onmessage = (ev: MessageEvent) => {
      if (!ev.data) return
      try {
        const payload = JSON.parse(ev.data) as StatusItem & { estateId: string }
        const { estateId, ...item } = payload
        if (!estateId || !item?.type) return
        setMap((prev) => {
          const estate = prev[estateId] ?? {}
          return {
            ...prev,
            [estateId]: {
              ...estate,
              [item.type]: item,
            },
          }
        })
      } catch {
        // ignore parse errors
      }
    }

    // If the server closes the stream (end-of-work), browsers may emit 'error' and try to reconnect.
    es.onerror = () => {
      try {
        es.close()
      } catch (e) {
        // ignore close errors
      }
    }

    return () => {
      try {
        es.close()
      } catch (e) {
        // ignore close errors
      }
    }
  }, [key, estateIds, status])

  return map
}

const StatusesCtx = React.createContext<EstateStatusMap | null>(null)

export function DocumentStatusesProvider({
  estateIds,
  status,
  children,
}: {
  estateIds: string[]
  status: GQLEstateTabFilter
  children: React.ReactNode
}) {
  const value = useBatchSSE(estateIds, status)
  return <StatusesCtx.Provider value={value}>{children}</StatusesCtx.Provider>
}

export function useEstateDocumentStatuses(estateId: string) {
  const ctx = React.useContext(StatusesCtx)
  return (ctx?.[estateId] ?? {}) as Record<string, StatusItem>
}
