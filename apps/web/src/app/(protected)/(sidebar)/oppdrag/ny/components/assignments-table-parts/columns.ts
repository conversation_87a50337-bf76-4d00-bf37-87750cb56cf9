import type { GQLEstateTabFilter } from '@/api/generated-client'

export type ColumnId =
  | 'address'
  | 'brokerTasks'
  | 'sellerTasks'
  | 'publish'
  | 'viewing'
  | 'brokers'

export function getColumnsForTab(tab?: GQLEstateTabFilter): ColumnId[] {
  switch (tab) {
    case 'Valuation':
      return ['address', 'brokerTasks', 'brokers']
    case 'Requested':
      return ['address', 'brokerTasks', 'sellerTasks', 'brokers']
    case 'InPreparation':
      return ['address', 'brokerTasks', 'sellerTasks', 'publish', 'brokers']
    case 'ForSale':
      return ['address', 'publish', 'viewing', 'brokers']
    case 'Sold':
      // Include a date column for Sold to display the sold date
      return ['address', 'sellerTasks', 'publish', 'viewing', 'brokers']
    case 'Archived':
      return ['address', 'brokers']
    default:
      return [
        'address',
        'brokerTasks',
        'sellerTasks',
        'publish',
        'viewing',
        'brokers',
      ]
  }
}

export function headerLabelFor(id: ColumnId, tab?: GQLEstateTabFilter): string {
  switch (id) {
    case 'address':
      return 'Adresse'
    case 'brokerTasks':
      return 'Meglers ansvar'
    case 'sellerTasks':
      return 'Selgers oppgaver'
    case 'publish':
      // In later stages, show relevant date column label
      if (tab === 'Sold') return 'Solgt'
      if (tab === 'ForSale' || tab === 'Archived') return 'Publisert'
      return 'Publiseres'
    case 'viewing':
      return 'Visning'
    case 'brokers':
      return ''
  }
}

export function headerClassFor(id: ColumnId): string {
  switch (id) {
    case 'address':
      return 'w-[36%] py-3 pl-4 text-left font-medium'
    case 'brokerTasks':
      return 'w-[12%] py-3 text-left font-medium'
    case 'sellerTasks':
      return 'w-[32%] py-3 text-left font-medium'
    case 'publish':
      return 'w-[16%] py-3 px-4 text-left font-medium'
    case 'viewing':
      return 'w-[25%] py-3 text-left font-medium'
    case 'brokers':
      return 'py-3 text-left font-medium'
  }
}
