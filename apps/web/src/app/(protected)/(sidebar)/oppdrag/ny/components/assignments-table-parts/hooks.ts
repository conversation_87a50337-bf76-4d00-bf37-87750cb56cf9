import { parseAsStringLiteral, useQueryState } from 'nuqs'
import React from 'react'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import type { SortDir, SortKey } from '../assignments-table'

import { TabSlug, ViewType, groupByWeek } from './util'

export function useSortingQueryState() {
  const [sort, setSort] = useQueryState<SortKey>(
    'sort',
    parseAsStringLiteral(['address', 'publish']).withDefault('address'),
  )
  const [dir, setDir] = useQueryState<SortDir>(
    'dir',
    parseAsStringLiteral(['asc', 'desc']).withDefault('asc'),
  )

  const sortKey = sort ?? 'address'
  const dirKey = dir ?? 'asc'

  const onSortAddress = React.useCallback(() => {
    if (sortKey !== 'address') {
      setSort('address')
      setDir('asc')
    } else {
      setDir(dirKey === 'asc' ? 'desc' : 'asc')
    }
  }, [sortKey, dirKey, setSort, setDir])

  const onSortPublish = React.useCallback(() => {
    if (sortKey !== 'publish') {
      setSort('publish')
      setDir('asc')
    } else {
      setDir(dirKey === 'asc' ? 'desc' : 'asc')
    }
  }, [sortKey, dirKey, setSort, setDir])

  const ariaSortFor = (key: SortKey): 'ascending' | 'descending' | 'none' => {
    if (sortKey !== key) return 'none'
    return dirKey === 'asc' ? 'ascending' : 'descending'
  }

  return { sortKey, dirKey, onSortAddress, onSortPublish, ariaSortFor }
}

export function useAssignmentsDerived(
  allEstates: GQLEstatesOverviewItemFragment[],
  sortKey: SortKey,
  dirKey: SortDir,
  currentTab?: GQLEstateTabFilter,
) {
  const sorted = React.useMemo(() => {
    const arr = [...allEstates]
    const mul = dirKey === 'asc' ? 1 : -1
    if (sortKey === 'publish') {
      arr.sort((a, b) => {
        const da = a.marketingStart ? new Date(a.marketingStart).getTime() : 0
        const db = b.marketingStart ? new Date(b.marketingStart).getTime() : 0
        return (da - db) * mul
      })
    } else {
      // To compare/sort house numbers
      const collator = new Intl.Collator('nb-NO', {
        sensitivity: 'base',
        numeric: true,
      })
      arr.sort((a, b) => {
        const aa = (a.address?.streetAddress || '').toLowerCase()
        const bb = (b.address?.streetAddress || '').toLowerCase()
        return collator.compare(aa, bb) * mul
      })
    }
    return arr
  }, [allEstates, sortKey, dirKey])

  const inPreperation = currentTab === 'InPreparation'

  const groups = React.useMemo(() => {
    if (!inPreperation)
      return [] as {
        key: number
        weekStart: Date
        estates: GQLEstatesOverviewItemFragment[]
      }[]
    return groupByWeek(sorted)
  }, [inPreperation, sorted])

  const withoutPublishDate = React.useMemo(
    () => (inPreperation ? sorted.filter((e) => !e.marketingStart) : []),
    [inPreperation, sorted],
  )

  const visibleCount = React.useMemo(() => {
    if (!inPreperation) return sorted.length
    const groupedCount = groups.reduce((sum, g) => sum + g.estates.length, 0)
    return groupedCount + withoutPublishDate.length
  }, [inPreperation, sorted.length, groups, withoutPublishDate.length])

  const visibleEstateIds = React.useMemo(() => {
    const estates = inPreperation
      ? [...groups.flatMap((g) => g.estates), ...withoutPublishDate]
      : sorted
    const ids = estates.map((e) => e.estateId).filter(Boolean)
    return Array.from(new Set(ids))
  }, [inPreperation, groups, withoutPublishDate, sorted])

  return {
    inPreperation,
    sorted,
    groups,
    withoutPublishDate,
    visibleCount,
    visibleEstateIds,
  }
}

export function useEstatesTab() {
  return useQueryState<TabSlug>(
    'tab',
    parseAsStringLiteral([
      'verdivurdering',
      'innslag',
      'klargjoring',
      'til-salg',
      'solgt',
      'arkiv',
    ]).withDefault('klargjoring'),
  )
}

export function useEstatesView() {
  return useQueryState<ViewType>(
    'view',
    parseAsStringLiteral(['kontor', 'ansatt']).withDefault('ansatt'),
  )
}
