'use client'

import { Search } from 'lucide-react'
import { parseAsBoolean, useQueryState } from 'nuqs'
import React from 'react'

import { Button } from '@nordvik/ui/button'
import { Input } from '@nordvik/ui/input'
import { Switch } from '@nordvik/ui/switch'

export function Controls({
  total,
  query,
  setQuery,
  onApply,
}: {
  total: number
  query: string
  setQuery: (v: string) => void
  onApply?: () => void
}) {
  const [onlyMine, setOnlyMine] = useQueryState(
    'mine',
    parseAsBoolean.withDefault(true),
  )
  return (
    <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
      <div className="typo-body-sm ink-muted flex items-center gap-3">
        <div>Viser {total} oppdrag</div>
        <div className="flex items-center gap-2">
          <Switch size="xs" checked={onlyMine} onCheckedChange={setOnlyMine} />
          <span>Kun egne</span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Input
          size="sm"
          placeholder="Søk adresse"
          value={query}
          onChange={(e) => setQuery(e.currentTarget.value)}
          addonLeft={<Search className="size-4" />}
          wrapperClassName="w-[260px]"
        />
        <Button variant="outline" size="sm" onClick={onApply}>
          Vis
        </Button>
      </div>
    </div>
  )
}
