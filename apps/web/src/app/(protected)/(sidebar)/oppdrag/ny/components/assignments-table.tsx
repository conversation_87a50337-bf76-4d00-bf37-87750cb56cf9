'use client'

import { useSearchParams } from 'next/navigation'
import { parseAsString, useQueryState } from 'nuqs'
import React from 'react'

import { Table, TableBody, TableFooter } from '@nordvik/ui/table'

import { useInfiniteNewAssignmentsOverviewQuery } from '@/api/generated-client'
import { useUserContext } from '@/lib/UserContext'

import { DocumentStatusesProvider } from '../client-fetching/use-document-statuses-batch-sse'

import { Controls } from './assignments-table-parts/controls'
import { GroupedBody } from './assignments-table-parts/grouped-body'
import {
  useAssignmentsDerived,
  useEstatesFromQuery,
  useSortingQueryState,
} from './assignments-table-parts/hooks'
import { LoadMoreButton } from './assignments-table-parts/load-more-button'
import { SkeletonRows } from './assignments-table-parts/skeleton-rows'
import { TableFooterInfo } from './assignments-table-parts/table-footer-info'
import { TableHeaderControls } from './assignments-table-parts/table-header-controls'
import { UngroupedBody } from './assignments-table-parts/ungrouped-body'
import { useEstatesTab } from './hooks'
import { TAB_SLUG_TO_ENUM } from './new-assignments-page'

export type SortKey = 'address' | 'publish'
export type SortDir = 'asc' | 'desc'

export function AssignmentsTable() {
  const PAGE_LIMIT = 15
  const searchParams = useSearchParams()
  const officeView = searchParams.get('view') === 'kontor'
  const [tab] = useEstatesTab()

  const currentTab = TAB_SLUG_TO_ENUM[tab]

  const [query, setQuery] = useQueryState(
    'search',
    parseAsString.withDefault(''),
  )

  const { sortKey, dirKey, onSortAddress, onSortPublish, ariaSortFor } =
    useSortingQueryState()

  // Build infinite query with initial server data
  const { user } = useUserContext()
  const { data, isError, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteNewAssignmentsOverviewQuery(
      {
        brokerId: user?.employeeId ?? '',
        departmentId: Number(user?.department?.departmentId) || 0,
        tabs: currentTab ? [currentTab] : [],
        limit: PAGE_LIMIT,
        offset: 0,
        archived: currentTab === 'Archived' ? true : undefined,
        search: query || undefined,
        officeView: officeView || undefined,
      },
      {
        enabled:
          Boolean(user?.employeeId) && Boolean(user?.department?.departmentId),
        initialPageParam: { offset: 0, limit: PAGE_LIMIT },
        getNextPageParam: (lastPage) => {
          const pag = officeView
            ? lastPage.office?.pagination
            : lastPage.broker?.pagination
          if (!pag) return undefined
          const nextOffset = (pag.offset || 0) + (pag.count || 0)
          if (nextOffset >= (pag.total || 0)) return undefined
          return { offset: nextOffset, limit: pag.limit || PAGE_LIMIT }
        },
        refetchOnWindowFocus: false,
      },
    )

  const { allEstates, totalFromQuery } = useEstatesFromQuery(
    data?.pages ?? [],
    officeView,
  )

  // Derived UI state (sorted, groups, counts, visible ids)
  const {
    inPreperation,
    sorted,
    groups,
    withoutPublishDate,
    visibleCount,
    visibleEstateIds,
  } = useAssignmentsDerived(allEstates, sortKey, dirKey, currentTab)

  return (
    <DocumentStatusesProvider estateIds={visibleEstateIds} status={currentTab!}>
      <Controls
        total={totalFromQuery ?? 0}
        query={query}
        setQuery={setQuery}
        onApply={() => setQuery(query, { shallow: false })}
      />
      <div className="mt-2 rounded-lg ">
        <Table className="text-sm">
          <TableHeaderControls
            ariaSortFor={ariaSortFor}
            onSortAddress={onSortAddress}
            onSortPublish={onSortPublish}
            currentTab={currentTab}
          />
          <TableBody alwaysLastBorder>
            {inPreperation ? (
              <GroupedBody
                groups={groups}
                withoutPublishDate={withoutPublishDate}
                currentTab={currentTab}
              />
            ) : (
              <UngroupedBody estates={sorted} currentTab={currentTab} />
            )}
            {isFetchingNextPage && (
              <SkeletonRows colSpan={undefined} currentTab={currentTab} />
            )}
          </TableBody>
          <TableFooter>
            <TableFooterInfo
              visibleCount={visibleCount}
              totalFromQuery={totalFromQuery}
              isError={isError}
            />
          </TableFooter>
        </Table>

        <LoadMoreButton
          hasNextPage={!!hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onClick={() => fetchNextPage()}
        />
      </div>
    </DocumentStatusesProvider>
  )
}
