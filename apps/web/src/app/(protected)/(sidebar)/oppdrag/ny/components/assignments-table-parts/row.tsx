'use client'

import Image from 'next/image'
import React from 'react'

import { Badge } from '@nordvik/ui/badge'
import { TableRow } from '@nordvik/ui/table'

import type {
  GQLEstateTabFilter,
  GQLEstatesOverviewItemFragment,
} from '@/api/generated-client'

import { DocumentStatuses } from '../../client-fetching/document-statuses'
import { CalendarPopover } from '../calendar-popover'
import { MiniRing } from '../mini-ring'

import { MemoBrokersCell } from './brokers-cell'
import { ColumnId, getColumnsForTab } from './columns'
import { MemoViewingCell } from './viewing-cell'

export function Row({
  estate,
  currentTab,
  columns,
}: {
  estate: GQLEstatesOverviewItemFragment
  currentTab?: GQLEstateTabFilter
  columns?: ColumnId[]
}) {
  const totalChecks = estate.checklist?.length || 0
  const completedChecks =
    estate.checklist?.filter((item) => item.value).length || 0
  const progress = totalChecks ? completedChecks / totalChecks : 0

  const cols = React.useMemo(
    () => columns ?? getColumnsForTab(currentTab),
    [columns, currentTab],
  )

  return (
    <TableRow className="align-top hover:bg-root-muted">
      {cols.map((c) => {
        if (c === 'address') {
          return (
            <td key={c} className="py-3 pl-4">
              <div className="flex items-start gap-3">
                {estate.mainImage?.small ? (
                  <Image
                    src={estate.mainImage.small}
                    alt={
                      estate.address?.streetAddress
                        ? `${estate.address.streetAddress} – bilde`
                        : 'Boligbilde'
                    }
                    width={48}
                    height={48}
                    sizes="48px"
                    className="size-12 shrink-0 rounded-md object-cover"
                  />
                ) : (
                  <div className="size-12 shrink-0 rounded-md bg-root-muted" />
                )}
                <div className="space-y-1">
                  <div className="font-medium leading-5">
                    {estate.address?.streetAddress ?? '—'}
                  </div>
                  <div className="typo-body-xs ink-muted flex items-center gap-2">
                    <span>Se i Next</span>
                    <span aria-hidden>↗</span>
                  </div>
                </div>
              </div>
            </td>
          )
        }
        if (c === 'brokerTasks') {
          return (
            <td key={c} className="py-3">
              <div className="flex items-center gap-2 ">
                <MiniRing progress={progress} />
                <div className="ink-muted">
                  <span className="font-medium">{completedChecks}</span>/
                  {totalChecks}
                </div>
              </div>
            </td>
          )
        }
        if (c === 'sellerTasks') {
          return (
            <td key={c} className="py-3">
              <div className="flex flex-wrap gap-2">
                <DocumentStatuses estateId={estate.estateId} />
              </div>
            </td>
          )
        }
        if (c === 'publish') {
          const isSold = currentTab === 'Sold'
          const isReadOnly = currentTab === 'ForSale' || isSold
          const date = isSold ? estate.soldDate : estate.marketingStart
          const d = date ? new Date(date) : undefined
          return (
            <td key={c} className="py-3 p-4">
              {isReadOnly ? (
                <Badge variant="grey" size="md">
                  {d
                    ? d.toLocaleDateString('no-NO', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                      })
                    : '—'}
                </Badge>
              ) : (
                <CalendarPopover publishDate={estate.marketingStart} />
              )}
            </td>
          )
        }
        if (c === 'viewing') {
          return (
            <td key={c} className="py-3">
              <MemoViewingCell showings={estate.showings} />
            </td>
          )
        }
        if (c === 'brokers') {
          return (
            <td key={c} className="p-3 justify-end">
              <MemoBrokersCell brokers={estate.brokers} />
            </td>
          )
        }
        return null
      })}
    </TableRow>
  )
}

export const MemoRow = React.memo(Row)
