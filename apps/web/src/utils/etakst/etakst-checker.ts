'use server'

import { notifyEtakstAvailable } from '@befaring/actions/notify-etakst-available'
import { getEtakstS3DocumentKey } from '@befaring/verdivurdering/[estateId]/din-bolig/components/get-etakst-s3-document-key'
import { subDays, subMinutes } from 'date-fns'
import { revalidatePath } from 'next/cache'
import { after } from 'next/server'

import {
  getEstateDocumentById,
  getLatestValuation,
} from '@/actions/next/estate-documents'
import { riskCompletionCheck } from '@/actions/risk-check-completed'
import { sendLeads } from '@/app/api/signicat/util/send-leads'
import prisma from '@/db/prisma'

import { storeEtakstInS3 } from './store-etakst-document'

export async function addToEtakstCheckQueue(estateId: string): Promise<void> {
  const oa = await prisma.listing_agreements.findFirst({
    where: { estate_id: estateId },
  })

  if (!oa?.is_valuation) {
    console.info(
      `Estate ${estateId} is not a valuation, skipping e-takst check queue.`,
    )
    return
  }

  const existingS3Key = await getEtakstS3DocumentKey(estateId).catch()

  try {
    await prisma.etakst_check_queue.upsert({
      where: { estate_id: estateId },
      update: {
        s3_document_key: existingS3Key?.s3_document_key,
      },
      create: {
        estate_id: estateId,
        created_at: new Date(),
        s3_document_key: existingS3Key?.s3_document_key,
      },
    })

    // trigger in the case it can be sent immediately
    after(() => processEtakstCheckQueue())
  } catch (error) {
    console.error(
      `Error adding estate ${estateId} to e-takst check queue:`,
      error,
    )
  }
}

interface EtakstQueueItem {
  id: string
  estate_id: string
  s3_document_key: string | null
}

async function processEtakstQueueItems(
  queueItems: EtakstQueueItem[],
  logLabel: string,
) {
  for (const item of queueItems) {
    const [valuation, riskCheckCompleted] = await Promise.all([
      getLatestValuation(item.estate_id),
      riskCompletionCheck(item.estate_id).then(({ isComplete }) => isComplete),
    ])

    const s3DocumentKey =
      item.s3_document_key ||
      (await getEtakstS3DocumentKey(item.estate_id).catch())?.s3_document_key

    if (valuation && !s3DocumentKey) {
      getEstateDocumentById(item.estate_id, valuation.documentId)
        .then(async (blob) => {
          if (blob) {
            await storeEtakstInS3(item.estate_id, valuation.documentId, blob)
          }
        })
        .catch((error) => {
          console.error(
            `Error fetching e-takst document for estate ${item.estate_id}:`,
            error,
          )
        })
    }

    const etakstAvailable = s3DocumentKey || valuation

    if (etakstAvailable && riskCheckCompleted) {
      await prisma.etakst_check_queue.update({
        where: { id: item.id },
        data: {
          is_complete: true,
          completed_at: new Date(),
          document_id: valuation?.documentId,
          last_checked_at: new Date(),
          check_count: { increment: 1 },
          s3_document_key: s3DocumentKey,
        },
      })

      const la = await notifyEtakstAvailable({
        estateId: item.estate_id,
        documentId: valuation?.documentId,
        channels: { email: true, sms: true },
      })

      if (la?.signicat_document_id) {
        await sendLeads(
          { externalId: item.estate_id, id: la.signicat_document_id },
          true,
        )
      } else {
        console.error(
          `[${logLabel}]: No Signicat document ID found for estate ${item.estate_id}, ${JSON.stringify(
            la,
          )}`,
        )

        const listingAgreement = await prisma.listing_agreements.findFirst({
          where: { estate_id: item.estate_id },
        })

        if (listingAgreement?.signicat_document_id) {
          await sendLeads(
            {
              externalId: item.estate_id,
              id: listingAgreement.signicat_document_id,
            },
            true,
          )
        }
      }
    } else {
      await prisma.etakst_check_queue.update({
        where: { id: item.id },
        data: {
          last_checked_at: new Date(),
          check_count: { increment: 1 },
        },
      })
    }

    revalidatePath(`/verdivurdering/${item.estate_id}`)
  }
}

export async function processEtakstCheckQueue(): Promise<void> {
  try {
    const tenMinutesAgo = subMinutes(new Date(), 10)
    const fifteenDaysAgo = subDays(new Date(), 15)
    const threeMonthsAgo = subDays(new Date(), 90)

    const queueItems = await prisma.etakst_check_queue.findMany({
      where: {
        is_complete: false,
        OR: [
          {
            last_checked_at: { lt: tenMinutesAgo },
            created_at: { gt: fifteenDaysAgo },
          },
          { last_checked_at: null },
          {
            created_at: { gt: threeMonthsAgo },
            s3_document_key: { not: { equals: null } },
          },
        ],
      },
      orderBy: [{ last_checked_at: { sort: 'asc', nulls: 'first' } }],
      take: 12,
    })

    await processEtakstQueueItems(queueItems, 'processEtakstCheckQueue')
  } catch (error) {
    console.error('Error processing e-takst check queue:', error)
  }
}

export async function processEtakstReminderBackfillQueue(): Promise<void> {
  try {
    const threeMonthsAgo = subDays(new Date(), 90)

    const queueItems = await prisma.etakst_check_queue.findMany({
      where: {
        is_complete: false,
        reminder_count: { gt: 0 },
        created_at: { gt: threeMonthsAgo },
      },
      orderBy: [{ last_checked_at: { sort: 'asc', nulls: 'first' } }],
      take: 12,
    })

    await processEtakstQueueItems(
      queueItems,
      'processEtakstReminderBackfillQueue',
    )
  } catch (error) {
    console.error('Error processing reminder backfill e-takst queue:', error)
  }
}
