export type EstateStatusKey = 'inpreparation' | 'sold'

// Three phases for a single-document provider
export type DocumentPhase = 'COMPLETE' | 'STARTED' | 'NONE' | 'ERROR'

export type RawProviderStatus = {
  type: string
  state: DocumentPhase
  updatedAt: string
  message?: string
}

export type ProviderDefinition = {
  type: string
  name: string
  timeoutMs?: number
  // Which estate statuses this provider applies to.
  enabledFor?: EstateStatusKey[]
  fetch: (estateId: string) => Promise<RawProviderStatus>
}

// Simple timeout helper so one slow provider does not block the whole set.
export async function withTimeout<T>(p: Promise<T>, ms: number): Promise<T> {
  let to: NodeJS.Timeout
  try {
    return await Promise.race<Promise<T>>([
      p,
      new Promise<T>((_, reject) => {
        to = setTimeout(() => reject(new Error('timeout')), ms)
      }),
    ])
  } finally {
    clearTimeout(to!)
  }
}
