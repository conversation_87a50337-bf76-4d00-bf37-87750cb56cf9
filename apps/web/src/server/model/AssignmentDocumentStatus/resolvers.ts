import type {
  GQLAssignmentDocumentState,
  GQLAssignmentDocumentType,
  GQLQueryResolvers,
} from '@/server/generated-schema'

import { getAssignmentDocumentStatus } from './factory'

export const Query: GQLQueryResolvers = {
  assignmentDocumentStatus: async (_parent, args) => {
    const result = await getAssignmentDocumentStatus(args.estateId, args.type)
    return {
      type: result.type as GQLAssignmentDocumentType,
      state: result.state as GQLAssignmentDocumentState,
      updatedAt: result.updatedAt,
      message: result.message ?? undefined,
    }
  },
}
