// Simulated slow providers – replace fetch implementations with real APIs.
// Each provider intentionally waits random 100-600ms (or more if simulated)
import {
  EstateStatusKey,
  ProviderDefinition,
  RawProviderStatus,
} from '../types'

// to emulate network / upstream variability.
const SIMULATED_MIN = 120
const SIMULATED_MAX = 5650

function simulateLatency() {
  const min = SIMULATED_MIN
  const max = SIMULATED_MAX
  const delay = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise((r) => setTimeout(r, delay))
}

async function simulateProvider(
  estateId: string,
  type: RawProviderStatus['type'],
  phase: RawProviderStatus['state'] = 'COMPLETE',
): Promise<RawProviderStatus> {
  console.info(`Simulating provider ${type} for estate ${estateId}`)
  await simulateLatency()
  return {
    type,
    state: phase,
    updatedAt: new Date().toISOString(),
  }
}

// Export provider definitions (each provider = one document)
export const providers: ProviderDefinition[] = [
  // Sold
  {
    type: 'TAKEOVER_PROTOCOL',
    name: 'Overtakelsesprotokoll',
    enabledFor: ['sold'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'TAKEOVER_PROTOCOL', 'COMPLETE'),
  },
  {
    type: 'SETTLEMENT_FORM_BUYER',
    name: 'Oppgjørsskjema kjøper',
    enabledFor: ['sold'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SETTLEMENT_FORM_BUYER', 'STARTED'),
  },
  {
    type: 'SETTLEMENT_FORM_SELLER',
    name: 'Oppgjørsskjema selger',
    enabledFor: ['sold'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SETTLEMENT_FORM_SELLER', 'NONE'),
  },

  // In preparation
  {
    type: 'SELLER_INTERVIEW',
    name: 'Selgers intervju',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SELLER_INTERVIEW', 'STARTED'),
  },
  {
    type: 'SELF_DECLARATION',
    name: 'Egenerklæring',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SELF_DECLARATION', 'COMPLETE'),
  },
  {
    type: 'ENERGY_CERTIFICATE',
    name: 'Energiattest',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'ENERGY_CERTIFICATE', 'STARTED'),
  },
  {
    type: 'SECURITY_OBLIGATION',
    name: 'Sikringsobligasjon',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SECURITY_OBLIGATION', 'COMPLETE'),
  },
  {
    type: 'SURVEY_REPORT',
    enabledFor: ['inpreparation'],
    name: 'Takstrapport',
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SURVEY_REPORT', 'COMPLETE'),
  },
  {
    type: 'PHOTOS',
    name: 'Foto',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) => simulateProvider(estateId, 'PHOTOS', 'NONE'),
  },
]

const map: Record<string, 'inpreparation' | 'sold'> = {
  inpreparation: 'inpreparation',
  in_preparation: 'inpreparation',
  'in-preparation': 'inpreparation',
  preparation: 'inpreparation',
  sold: 'sold',
}

export const providerTypesForStatus = (status: EstateStatusKey) => {
  const normalized = (status ?? '').trim().toLowerCase()
  const key = map[normalized]

  return providers
    .filter((p) => !p.enabledFor || p.enabledFor.includes(key))
    .map((p) => ({ type: p.type, name: p.name }))
}

export function providersForStatus(status?: string) {
  const normalized = (status ?? '').trim().toLowerCase()
  const key = map[normalized]

  const selected =
    key != null
      ? providers.filter((p) => !p.enabledFor || p.enabledFor.includes(key))
      : providers

  return selected.length > 0 ? selected : providers
}
