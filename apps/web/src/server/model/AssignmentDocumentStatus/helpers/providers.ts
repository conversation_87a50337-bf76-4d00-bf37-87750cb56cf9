// Simulated slow providers – replace fetch implementations with real APIs.
// Each provider intentionally waits random 100-600ms (or more if simulated)
import { getEstateDocuments } from '@/actions/next/estate-documents'
import { estateImages } from '@/actions/next/estate-images'
import { DocumentTypeEnum } from '@/actions/next/types-next'
import prisma from '@/db/prisma'
import { EstateFormType, getFormStatus } from '@/utils/forms'

import {
  EstateStatusKey,
  ProviderDefinition,
  RawProviderStatus,
} from '../types'

// to emulate network / upstream variability.
const SIMULATED_MIN = 120
const SIMULATED_MAX = 5650

function simulateLatency() {
  const min = SIMULATED_MIN
  const max = SIMULATED_MAX
  const delay = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise((r) => setTimeout(r, delay))
}

async function simulateProvider(
  estateId: string,
  type: RawProviderStatus['type'],
  phase: RawProviderStatus['state'] = 'COMPLETE',
): Promise<RawProviderStatus> {
  console.info(`Simulating provider ${type} for estate ${estateId}`)
  await simulateLatency()
  return {
    type,
    state: phase,
    updatedAt: new Date().toISOString(),
  }
}

// Export provider definitions (each provider = one document)
export const providers: ProviderDefinition[] = [
  // Sold
  {
    type: 'TAKEOVER_PROTOCOL',
    name: 'Overtakelsesprotokoll',
    enabledFor: ['sold'],
    fetch: async (estateId: string) => {
      const status = await getFormStatus(estateId, EstateFormType.Otp)

      return {
        type: 'TAKEOVER_PROTOCOL',
        state: status?.signingFinished
          ? 'COMPLETE'
          : status?.isNotificationSent
            ? 'STARTED'
            : 'NONE',
        updatedAt: undefined,
      }
    },
  },
  {
    type: 'SETTLEMENT_FORM_BUYER',
    name: 'Oppgjørsskjema kjøper',
    enabledFor: ['sold'],
    fetch: async (estateId: string) => {
      const status = await getFormStatus(
        estateId,
        EstateFormType.SettlementBuyer,
      )

      return {
        type: 'SETTLEMENT_FORM_BUYER',
        state: status?.signingFinished
          ? 'COMPLETE'
          : status?.isNotificationSent
            ? 'STARTED'
            : 'NONE',
        updatedAt: undefined,
      }
    },
  },
  {
    type: 'SETTLEMENT_FORM_SELLER',
    name: 'Oppgjørsskjema selger',
    enabledFor: ['sold'],
    fetch: async (estateId: string) => {
      const status = await getFormStatus(
        estateId,
        EstateFormType.SettlementSeller,
      )

      return {
        type: 'SETTLEMENT_FORM_SELLER',
        state: status?.signingFinished
          ? 'COMPLETE'
          : status?.isNotificationSent
            ? 'STARTED'
            : 'NONE',
        updatedAt: undefined,
      }
    },
  },
  {
    type: 'SETTLEMENT_FORM_BUYER_PROJECT',
    name: 'Oppgjørsskjema kjøper (prosjekt)',
    enabledFor: ['sold'],
    fetch: async (estateId: string) => {
      const status = await getFormStatus(
        estateId,
        EstateFormType.SettlementBuyerProject,
      )

      return {
        type: 'SETTLEMENT_FORM_BUYER_PROJECT',
        state: status?.signingFinished
          ? 'COMPLETE'
          : status?.isNotificationSent
            ? 'STARTED'
            : 'NONE',
        updatedAt: undefined,
      }
    },
  },

  // In preparation
  {
    type: 'SELLER_INTERVIEW',
    name: 'Selgers intervju',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SELLER_INTERVIEW', 'STARTED'),
  },
  {
    type: 'SELF_DECLARATION',
    name: 'Egenerklæring',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SELF_DECLARATION', 'COMPLETE'),
  },
  {
    type: 'ENERGY_CERTIFICATE',
    name: 'Energiattest',
    enabledFor: ['inpreparation'],
    fetch: async (estateId: string) => {
      const documents = await getEstateDocuments(estateId, [
        DocumentTypeEnum.EnergyCertificate,
      ])

      return {
        type: 'ENERGY_CERTIFICATE',
        state: documents.length > 0 ? 'COMPLETE' : 'NONE',
        updatedAt: documents[0]?.lastChanged ?? undefined,
      }
    },
  },
  {
    type: 'SECURITY_OBLIGATION',
    name: 'Sikringsobligasjon',
    enabledFor: ['inpreparation'],
    fetch: (estateId: string) =>
      simulateProvider(estateId, 'SECURITY_OBLIGATION', 'COMPLETE'),
  },
  {
    type: 'SURVEY_REPORT',
    enabledFor: ['inpreparation'],
    name: 'Takstrapport',
    fetch: async (estateId: string) => {
      const documents = await getEstateDocuments(estateId, [
        DocumentTypeEnum.PropertyValuation,
      ])

      return {
        type: 'SURVEY_REPORT',
        state: documents.length > 0 ? 'COMPLETE' : 'NONE',
        updatedAt: documents[0]?.lastChanged ?? undefined,
      }
    },
  },
  {
    type: 'PHOTOS',
    name: 'Foto',
    enabledFor: ['inpreparation'],
    fetch: async (estateId: string) => {
      const photos = await estateImages(estateId)

      const lastChangedDate = photos
        ?.map((p) => new Date(p.lastChanged))
        .sort((a, b) => b.getTime() - a.getTime())[0]
        ?.toISOString()

      return {
        type: 'PHOTOS',
        state: photos && photos?.length > 0 ? 'COMPLETE' : 'NONE',
        updatedAt: lastChangedDate,
      }
    },
  },

  // Request
  {
    type: 'LISTING_AGREEMENT',
    name: 'Oppdragsavtale',
    enabledFor: ['inpreparation'],
    fetch: async (estateId: string) => {
      const inspection = await prisma.inspection_folders.findUnique({
        where: { estate_id: estateId },
        select: {
          sent_at: true,
          updated_at: true,
          listing_agreement: {
            select: { signing_finished_at: true, updated_at: true },
          },
        },
      })

      return {
        type: 'LISTING_AGREEMENT',
        state: inspection?.listing_agreement?.signing_finished_at
          ? 'COMPLETE'
          : inspection?.sent_at
            ? 'STARTED'
            : 'NONE',
        updatedAt: (
          inspection?.listing_agreement?.updated_at ?? inspection?.updated_at
        )?.toISOString(),
      }
    },
  },
]

const map: Record<string, 'inpreparation' | 'sold'> = {
  inpreparation: 'inpreparation',
  in_preparation: 'inpreparation',
  'in-preparation': 'inpreparation',
  preparation: 'inpreparation',
  sold: 'sold',
}

export const providerTypesForStatus = (status: EstateStatusKey) => {
  const normalized = (status ?? '').trim().toLowerCase()
  const key = map[normalized]

  return providers
    .filter((p) => !p.enabledFor || p.enabledFor.includes(key))
    .map((p) => ({ type: p.type, name: p.name }))
}

export function providersForStatus(status?: string) {
  const normalized = (status ?? '').trim().toLowerCase()
  const key = map[normalized]

  const selected =
    key != null
      ? providers.filter((p) => !p.enabledFor || p.enabledFor.includes(key))
      : providers

  return selected.length > 0 ? selected : providers
}
