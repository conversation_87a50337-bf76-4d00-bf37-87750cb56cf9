import { MenuIcon } from 'lucide-react'
import type { Session } from 'next-auth'

import { cn } from '@nordvik/theme/cn'
import {
  Sheet,
  SheetContent,
  SheetOverlay,
  SheetTrigger,
} from '@nordvik/ui/sheet'

import { useUserContext } from '@/lib/UserContext'

import NavMenuContent from './nav-menu-content'

export function NavigationSheet({ user }: { user?: Session['user'] }) {
  const { notifications } = useUserContext()

  return (
    <Sheet>
      <SheetTrigger className="relative h-min rounded-full p-0 ink-[white] lg:hidden">
        <MenuIcon className="size-[27px]" />
        {notifications.total > 0 && (
          <div
            className={cn(
              'absolute right-0 top-0 aspect-square size-2 outline outline-[3px] max-lg:outline-[#193d41ff] lg:outline-background-root rounded-full bg-gold-emphasis',
            )}
          />
        )}
      </SheetTrigger>
      <SheetOverlay className="bg-overlay animate-in fade-in-0" />
      <SheetContent
        className="flex w-full max-w-[310px] grow px-0 bg-root-muted animate-in slide-in-from-right-1/2 ease-out"
        data-theme="dark"
      >
        <NavMenuContent user={user} />
      </SheetContent>
    </Sheet>
  )
}
