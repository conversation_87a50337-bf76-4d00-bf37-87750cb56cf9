type IconProps = React.HTMLAttributes<SVGElement>

export const Icons = {
  google: (props: IconProps) => (
    <svg viewBox="0 0 48 48" fill="none" {...props}>
      <g clip-path="url(#clip)">
        <path
          d="M23.9996 19.6367V28.9313H36.916C36.3488 31.9204 34.6468 34.4514 32.0941 36.1532L39.8831 42.1968C44.4213 38.0079 47.0395 31.8551 47.0395 24.546C47.0395 22.8443 46.8868 21.2077 46.6031 19.637L23.9996 19.6367Z"
          fill="currentColor"
        />
        <path
          d="M10.5494 28.5664L8.79263 29.9112L2.57434 34.7547C6.52342 42.5874 14.6174 47.9984 23.9991 47.9984C30.4789 47.9984 35.9116 45.8602 39.8826 42.1948L32.0936 36.1511C29.9554 37.5911 27.2281 38.4639 23.9991 38.4639C17.7591 38.4639 12.4575 34.253 10.5592 28.5803L10.5494 28.5664Z"
          fill="currentColor"
        />
        <path
          d="M2.57436 13.2422C0.938084 16.4711 0 20.1148 0 23.9984C0 27.882 0.938084 31.5257 2.57436 34.7546C2.57436 34.7763 10.5599 28.5583 10.5599 28.5583C10.08 27.1183 9.79624 25.5911 9.79624 23.9982C9.79624 22.4052 10.08 20.878 10.5599 19.438L2.57436 13.2422Z"
          fill="currentColor"
        />
        <path
          d="M23.9996 9.55636C27.5342 9.55636 30.676 10.7781 33.1851 13.1345L40.0577 6.2619C35.8904 2.37833 30.4797 0 23.9996 0C14.6179 0 6.52342 5.38908 2.57434 13.2437L10.5597 19.44C12.4578 13.7672 17.7596 9.55636 23.9996 9.55636Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip">
          <rect width="48" height="48" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  apple: (props: IconProps) => (
    <svg viewBox="0 0 18 23" fill="none" {...props}>
      <path
        d="M14.8129 11.8355C14.8237 11.0008 15.0454 10.1824 15.4574 9.45649C15.8694 8.73054 16.4584 8.1206 17.1695 7.68343C16.7178 7.03827 16.1218 6.50732 15.429 6.13277C14.7362 5.75822 13.9655 5.55038 13.1783 5.52575C11.4991 5.34949 9.87115 6.53058 9.01544 6.53058C8.14317 6.53058 6.82567 5.54325 5.40694 5.57244C4.48926 5.60208 3.59492 5.86894 2.81104 6.347C2.02716 6.82505 1.38048 7.49801 0.934018 8.3003C-0.999982 11.6487 0.442607 16.5698 2.29522 19.2764C3.22212 20.6017 4.3054 22.0822 5.72285 22.0297C7.10991 21.9722 7.62795 21.1452 9.30225 21.1452C10.961 21.1452 11.447 22.0297 12.8932 21.9963C14.3816 21.9721 15.3194 20.6651 16.2138 19.3272C16.8797 18.3828 17.3922 17.3391 17.7322 16.2347C16.8675 15.8689 16.1296 15.2567 15.6104 14.4745C15.0913 13.6922 14.8139 12.7744 14.8129 11.8355Z"
        fill="currentColor"
      />
      <path
        d="M12.0805 3.74843C12.8921 2.77422 13.2919 1.52204 13.1951 0.257812C11.9552 0.388033 10.81 0.980592 9.98748 1.91742C9.58534 2.37509 9.27734 2.90752 9.0811 3.48429C8.88485 4.06106 8.80421 4.67086 8.84377 5.27881C9.46391 5.2852 10.0774 5.15078 10.6381 4.8857C11.1987 4.62061 11.6919 4.23176 12.0805 3.74843Z"
        fill="currentColor"
      />
    </svg>
  ),
  playStore: (props: IconProps) => (
    <svg viewBox="0 0 24 26" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.18691 0.536082C0.898818 0.847803 0.732422 1.32411 0.732422 1.94755V24.0598C0.732422 24.6832 0.898818 25.1595 1.19684 25.4588L1.27383 25.5261L13.612 13.1371V12.8603L1.2639 0.46875L1.18691 0.536082Z"
        fill="url(#paint0_linear_4824_21159)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.7168 17.2802L13.6016 13.148V12.8587L17.7168 8.72656L17.8062 8.78143L22.674 11.5595C24.0672 12.3475 24.0672 13.6493 22.674 14.4473L17.8062 17.2253L17.7168 17.2802Z"
        fill="url(#paint1_linear_4824_21159)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.8054 17.2219L13.6007 13L1.18555 25.4663C1.64003 25.9551 2.40248 26.01 3.25433 25.5337L17.8054 17.2219Z"
        fill="url(#paint2_linear_4824_21159)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.8054 8.77453L3.25433 0.475276C2.40248 -0.0135018 1.63755 0.0538301 1.18555 0.542608L13.6007 12.9965L17.8054 8.77453Z"
        fill="url(#paint3_linear_4824_21159)"
      />
      <path
        opacity="0.2"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.7159 17.1328L3.26411 25.3772C2.45696 25.8435 1.73673 25.8111 1.2723 25.3871L1.19531 25.4645L1.2723 25.5318C1.73673 25.9532 2.45696 25.9881 3.26411 25.5218L17.8152 17.2226L17.7159 17.1328Z"
        fill="black"
      />
      <path
        opacity="0.12"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.6727 14.2993L17.7031 17.1322L17.7925 17.2219L22.6603 14.4439C23.3581 14.0449 23.7009 13.5212 23.7009 13C23.6586 13.4788 23.3035 13.9327 22.6727 14.2993Z"
        fill="black"
      />
      <path
        opacity="0.25"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.25569 0.624271L22.6745 11.7016C23.3053 12.0582 23.6604 12.5245 23.7151 13.0008C23.7151 12.4796 23.3723 11.9559 22.6745 11.5569L3.25569 0.479632C1.86243 -0.320867 0.732422 0.347463 0.732422 1.94597V2.0906C0.732422 0.489607 1.86243 -0.166253 3.25569 0.624271Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4824_21159"
          x1="12.5053"
          y1="1.70899"
          x2="-7.26909"
          y2="6.95629"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00A0FF" />
          <stop offset="0.00657" stopColor="#00A1FF" />
          <stop offset="0.2601" stopColor="#00BEFF" />
          <stop offset="0.5122" stopColor="#00D2FF" />
          <stop offset="0.7604" stopColor="#00DFFF" />
          <stop offset="1" stopColor="#00E3FF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4824_21159"
          x1="24.4909"
          y1="13.0034"
          x2="0.39318"
          y2="13.0034"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE000" />
          <stop offset="0.4087" stopColor="#FFBD00" />
          <stop offset="0.7754" stopColor="#FFA500" />
          <stop offset="1" stopColor="#FF9C00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4824_21159"
          x1="15.5191"
          y1="15.2973"
          x2="-0.443719"
          y2="42.0538"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF3A44" />
          <stop offset="1" stopColor="#C31162" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4824_21159"
          x1="-1.94073"
          y1="-6.82473"
          x2="5.18092"
          y2="5.12516"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#32A071" />
          <stop offset="0.0685" stopColor="#2DA771" />
          <stop offset="0.4762" stopColor="#15CF74" />
          <stop offset="0.8009" stopColor="#06E775" />
          <stop offset="1" stopColor="#00F076" />
        </linearGradient>
      </defs>
    </svg>
  ),
  snapchat: (props: IconProps) => (
    <svg role="img" viewBox="0 0 48 48" fill="none" {...props}>
      <path
        d="M47.8265 34.9168C47.4955 34.0098 46.8582 33.5195 46.135 33.1273C46.0001 33.0537 45.8776 32.9802 45.7673 32.9311C45.5466 32.8208 45.326 32.7105 45.1054 32.6002C42.8501 31.399 41.085 29.9036 39.8716 28.1141C39.5284 27.6115 39.2219 27.0722 38.9768 26.5206C38.8665 26.2265 38.8787 26.0549 38.9523 25.8955C39.0258 25.7729 39.1239 25.6749 39.2464 25.5891C39.6387 25.3317 40.0309 25.0743 40.3006 24.9027C40.7786 24.584 41.1708 24.3389 41.416 24.1673C42.3353 23.5176 42.9849 22.8312 43.3894 22.059C43.9655 20.9804 44.039 19.7179 43.5977 18.578C42.9849 16.96 41.465 15.9672 39.6142 15.9672C39.2219 15.9672 38.842 16.004 38.4497 16.0898C38.3517 16.1143 38.2414 16.1388 38.1433 16.1633C38.1556 15.0602 38.131 13.8957 38.033 12.7436C37.6898 8.71095 36.2679 6.60272 34.7971 4.92349C33.8533 3.86937 32.7501 2.9746 31.5122 2.27594C29.2814 1.00119 26.7441 0.351562 23.9863 0.351562C21.2284 0.351562 18.7034 1.00119 16.4726 2.27594C15.2346 2.9746 14.1315 3.86937 13.1877 4.92349C11.7168 6.60272 10.3073 8.72321 9.95179 12.7436C9.85373 13.8957 9.82922 15.0602 9.84148 16.1633C9.74342 16.1388 9.64536 16.1143 9.53505 16.0898C9.15507 16.004 8.76285 15.9672 8.38287 15.9672C6.53204 15.9672 5.01215 16.9723 4.39929 18.578C3.95803 19.7179 4.03157 20.9804 4.60766 22.059C5.01215 22.8312 5.66178 23.5176 6.58107 24.1673C6.82621 24.3389 7.20618 24.584 7.69647 24.9027C7.95387 25.0743 8.33384 25.3194 8.71382 25.5646C8.84864 25.6504 8.95896 25.7607 9.04476 25.8955C9.1183 26.0549 9.13056 26.2265 9.00799 26.5451C8.76284 27.0845 8.46867 27.6115 8.12547 28.1018C6.93653 29.8423 5.22052 31.3255 3.03874 32.5144C1.88657 33.1273 0.685365 33.5317 0.170564 34.9168C-0.209408 35.9587 0.035735 37.1354 1.00405 38.1404C1.35951 38.5082 1.77625 38.8268 2.22977 39.072C3.17357 39.5868 4.17866 39.9913 5.23278 40.2732C5.45341 40.3345 5.64952 40.4203 5.83338 40.5428C6.18884 40.8493 6.13981 41.315 6.60558 42.0014C6.83847 42.3569 7.1449 42.6633 7.4881 42.9085C8.48093 43.5949 9.59633 43.6317 10.773 43.6807C11.8394 43.7175 13.0406 43.7665 14.4257 44.22C15.0017 44.4039 15.5901 44.7716 16.2765 45.2006C17.9312 46.2179 20.1865 47.603 23.974 47.603C27.7615 47.603 30.029 46.2057 31.696 45.1883C32.3824 44.7716 32.9708 44.4039 33.5223 44.22C34.8951 43.7665 36.1086 43.7175 37.175 43.6807C38.3517 43.6317 39.4671 43.5949 40.4599 42.9085C40.8767 42.6143 41.2198 42.2466 41.465 41.8053C41.8082 41.2292 41.7959 40.8248 42.1146 40.5428C42.2862 40.4203 42.4823 40.3345 42.6785 40.2854C43.7326 40.0035 44.7622 39.599 45.7182 39.072C46.1963 38.8146 46.6375 38.4714 47.0052 38.0669L47.0175 38.0546C47.9736 37.0741 48.2064 35.9219 47.8265 34.9168ZM44.468 36.7186C42.4211 37.8463 41.0483 37.7237 39.9941 38.4101C39.0871 38.9862 39.6264 40.2364 38.9768 40.6899C38.1678 41.2415 35.7899 40.6532 32.7256 41.6705C30.1884 42.504 28.5827 44.9187 24.023 44.9187C19.4634 44.9187 17.8944 42.5163 15.3204 41.6705C12.2561 40.6532 9.87825 41.2538 9.06927 40.6899C8.41964 40.2364 8.9467 38.9862 8.05193 38.4101C6.98556 37.7237 5.62501 37.8463 3.57806 36.7186C2.26654 35.9954 3.01423 35.5542 3.44323 35.3458C10.8711 31.7544 12.06 26.2019 12.1091 25.7852C12.1703 25.2827 12.2439 24.8904 11.6923 24.3879C11.1653 23.8976 8.81187 22.439 8.14999 21.9855C7.07135 21.2255 6.59333 20.4778 6.94878 19.5463C7.19393 18.9089 7.79453 18.6638 8.41964 18.6638C8.61576 18.6638 8.81187 18.6883 9.00799 18.7251C10.1969 18.9825 11.3491 19.5708 12.011 19.7424C12.0968 19.7669 12.1703 19.7792 12.2561 19.7792C12.6116 19.7792 12.7342 19.5953 12.7097 19.1908C12.6361 17.8916 12.4523 15.3666 12.6606 13.001C12.9425 9.75282 13.9844 8.13487 15.2346 6.71304C15.8352 6.02664 18.6421 3.0604 24.023 3.0604C29.4039 3.0604 32.2108 6.01438 32.8114 6.70078C34.0617 8.12261 35.1035 9.74056 35.3854 12.9887C35.5938 15.3543 35.4099 17.8793 35.3241 19.1786C35.2996 19.6076 35.4222 19.7669 35.7777 19.7669C35.8635 19.7669 35.937 19.7547 36.0228 19.7302C36.6847 19.5708 37.8369 18.9702 39.0258 18.7128C39.2219 18.6638 39.418 18.6515 39.6142 18.6515C40.2393 18.6515 40.8399 18.8967 41.085 19.534C41.4405 20.4656 40.9625 21.2133 39.8838 21.9732C39.2342 22.4267 36.8808 23.8853 36.3415 24.3756C35.7899 24.8782 35.8635 25.2704 35.9248 25.7729C35.9738 26.1897 37.1627 31.7422 44.5906 35.3335C45.0318 35.5419 45.7673 35.9954 44.468 36.7186Z"
        fill="currentColor"
      />
    </svg>
  ),
  pdf: (props: IconProps) => (
    <svg viewBox="0 0 24 24" fill="none" {...props}>
      <path
        d="M3 20V4C3 3.20435 3.3163 2.44152 3.87891 1.87891C4.44152 1.3163 5.20435 1 6 1H15L15.0986 1.00488C15.3276 1.02757 15.5429 1.12883 15.707 1.29297L20.707 6.29297C20.8946 6.48051 21 6.73478 21 7V20C21 20.7957 20.6837 21.5585 20.1211 22.1211C19.5585 22.6837 18.7957 23 18 23H6C5.20435 23 4.44152 22.6837 3.87891 22.1211C3.3163 21.5585 3 20.7957 3 20ZM15 6C15 6.26522 15.1054 6.5195 15.293 6.70703C15.4805 6.89457 15.7348 7 16 7H18.5859L15 3.41406V6ZM5 20C5 20.2652 5.10543 20.5195 5.29297 20.707C5.48051 20.8946 5.73478 21 6 21H18C18.2652 21 18.5195 20.8946 18.707 20.707C18.8946 20.5195 19 20.2652 19 20V9H16C15.2044 9 14.4415 8.6837 13.8789 8.12109C13.3163 7.55849 13 6.79565 13 6V3H6C5.73478 3 5.48051 3.10543 5.29297 3.29297C5.10543 3.48051 5 3.73478 5 4V20Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.40899 17.2212C8.59476 17.3769 8.84844 17.4996 9.1527 17.4887C9.4466 17.4781 9.67758 17.3466 9.8361 17.2142C10.1291 16.9695 10.3304 16.5933 10.4738 16.2582C10.5049 16.1857 10.5352 16.1101 10.5648 16.0319C10.9145 15.903 11.3129 15.7806 11.7289 15.6755C12.135 15.573 12.5357 15.4926 12.9001 15.4393C12.9752 15.4985 13.0493 15.5544 13.1222 15.6067C13.4258 15.8244 13.7912 16.0409 14.1621 16.1079C14.3592 16.1436 14.6192 16.148 14.8777 16.0238C15.1518 15.8921 15.3229 15.6651 15.414 15.4295C15.4953 15.2194 15.5373 14.9479 15.436 14.6655C15.3352 14.3848 15.1337 14.2076 14.9531 14.1032C14.6277 13.915 14.2173 13.8722 13.8855 13.8653C13.7282 13.862 13.5601 13.8668 13.3841 13.8786C13.0223 13.5435 12.6293 13.1281 12.2482 12.6778C11.9174 12.287 11.6091 11.8861 11.3487 11.5096C11.3528 11.2476 11.3472 10.9989 11.3306 10.771C11.3031 10.393 11.2409 9.9898 11.0909 9.67191C11.0149 9.51077 10.8842 9.30174 10.6564 9.15636C10.3972 8.99088 10.0986 8.96279 9.8256 9.04367C9.54894 9.12565 9.31695 9.31418 9.19032 9.58945C9.07896 9.8315 9.07886 10.0756 9.09968 10.2531C9.1408 10.6037 9.29991 10.9819 9.47715 11.3228C9.57861 11.5179 9.69838 11.7238 9.8323 11.9358C9.8071 12.3765 9.7559 12.8589 9.68166 13.3439C9.59488 13.9108 9.48092 14.4533 9.3522 14.913C9.21825 14.9764 9.09217 15.0416 8.97598 15.1083C8.68978 15.2725 8.34637 15.5099 8.1539 15.841C8.04589 16.0269 7.96269 16.2831 8.01717 16.5762C8.07112 16.8665 8.23898 17.0788 8.40899 17.2212ZM11.3616 14.2212C11.253 14.2486 11.144 14.2775 11.0352 14.3077C11.0754 14.1052 11.112 13.9003 11.1448 13.6959C11.2818 13.8566 11.4225 14.0159 11.5652 14.1713C11.497 14.1875 11.4291 14.2041 11.3616 14.2212Z"
        fill="currentColor"
      />
    </svg>
  ),
  standby: (props: IconProps) => (
    <svg viewBox="0 0 16 16" fill="none" {...props}>
      <path
        d="M12.6667 8C12.6667 5.42267 10.5773 3.33333 8 3.33333C5.42267 3.33333 3.33333 5.42267 3.33333 8C3.33333 10.5773 5.42267 12.6667 8 12.6667V14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8C14 11.3137 11.3137 14 8 14V12.6667C10.5773 12.6667 12.6667 10.5773 12.6667 8Z"
        fill="#B6A364"
      />
      <path
        d="M8 11.3333C8.88406 11.3333 9.7319 10.9821 10.357 10.357C10.9821 9.7319 11.3333 8.88406 11.3333 8C11.3333 7.11595 10.9821 6.2681 10.357 5.64298C9.7319 5.01786 8.88406 4.66667 8 4.66667L8 11.3333Z"
        fill="#B6A364"
      />
    </svg>
  ),
}
